// Get token
const token = localStorage.getItem('token');

// If no token, redirect to login page
if (!token) {
    window.location.href = '/login';
}

// Load current settings
async function loadSettings() {
    try {
        const response = await fetch('/web/admin/api/config', {
            headers: {
                'Authorization': `Bear<PERSON> ${token}`
            }
        });

        if (response.status === 401) {
            // Token expired, redirect to login page
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error('Failed to load settings');
        }

        const config = await response.json();

        // Update Webhook settings
        document.getElementById('webhookEnable').checked = config.webhook_enable;
        document.getElementById('webhookURL').value = config.webhook_url || '';
        document.getElementById('webhookKey').value = config.webhook_key || '';

    } catch (error) {
        console.error('Error loading settings:', error);
        alert('Failed to load settings');
    }
}

// 处理密码修改
document.getElementById('passwordForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (newPassword !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }

    try {
        const response = await fetch('/web/admin/api/config', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                admin_pass: newPassword
            })
        });

        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error('Failed to update password');
        }

        alert('密码修改成功');
        document.getElementById('passwordForm').reset();
    } catch (error) {
        console.error('Error updating password:', error);
        alert('密码修改失败');
    }
});

// 处理Webhook设置
document.getElementById('webhookForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const webhookEnable = document.getElementById('webhookEnable').checked;
    const webhookURL = document.getElementById('webhookURL').value;
    const webhookKey = document.getElementById('webhookKey').value;

    try {
        const response = await fetch('/web/admin/api/config', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                webhook_enable: webhookEnable,
                webhook_url: webhookURL,
                webhook_key: webhookKey
            })
        });

        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error('Failed to update webhook settings');
        }

        alert('Webhook设置保存成功');
    } catch (error) {
        console.error('Error updating webhook settings:', error);
        alert('Webhook设置保存失败');
    }
});

// 页面加载完成后加载设置
document.addEventListener('DOMContentLoaded', loadSettings); 