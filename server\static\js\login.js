// Enhanced login form submission with improved UX
async function handleLogin(event) {
    event.preventDefault();

    const form = event.target;
    const loginButton = document.getElementById('loginButton');
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    // Clear previous validation states
    clearValidation();

    // Get form data
    const username = usernameField.value.trim();
    const password = passwordField.value;

    // Client-side validation
    if (!validateForm(username, password)) {
        return false;
    }

    // Set loading state
    setLoadingState(true);

    try {
        // Send login request with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch('/web/admin/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        const data = await response.json();

        if (response.ok) {
            // Save token to localStorage
            localStorage.setItem('token', data.token);

            // Show success message
            showSuccess('Login successful! Redirecting...');

            // Add a small delay for better UX
            setTimeout(() => {
                window.location.href = '/web/admin/beacons';
            }, 1000);
        } else {
            // Login failed, show error message
            showError(data.error || 'Invalid username or password');

            // Focus on username field for retry
            usernameField.focus();
        }
    } catch (error) {
        console.error('Login error:', error);

        if (error.name === 'AbortError') {
            showError('Request timeout. Please check your connection and try again.');
        } else {
            showError('Network error. Please check your connection and try again.');
        }
    } finally {
        setLoadingState(false);
    }

    return false;
}

// Validate form inputs
function validateForm(username, password) {
    let isValid = true;
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    if (!username) {
        showFieldError(usernameField, 'Username is required');
        isValid = false;
    } else if (username.length < 3) {
        showFieldError(usernameField, 'Username must be at least 3 characters');
        isValid = false;
    }

    if (!password) {
        showFieldError(passwordField, 'Password is required');
        isValid = false;
    } else if (password.length < 6) {
        showFieldError(passwordField, 'Password must be at least 6 characters');
        isValid = false;
    }

    return isValid;
}

// Show field-specific error
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.textContent = message;
    }
}

// Clear validation states
function clearValidation() {
    const fields = document.querySelectorAll('.form-control');
    fields.forEach(field => {
        field.classList.remove('is-invalid', 'is-valid');
    });
}

// Set loading state
function setLoadingState(loading) {
    const loginButton = document.getElementById('loginButton');
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');

    if (loading) {
        loginButton.classList.add('loading');
        loginButton.disabled = true;
        usernameField.disabled = true;
        passwordField.disabled = true;
    } else {
        loginButton.classList.remove('loading');
        loginButton.disabled = false;
        usernameField.disabled = false;
        passwordField.disabled = false;
    }
}

// Show error message with enhanced styling
function showError(message) {
    showAlert(message, 'danger', 'bi-exclamation-triangle');
}

// Show success message
function showSuccess(message) {
    showAlert(message, 'success', 'bi-check-circle');
}

// Generic alert function
function showAlert(message, type, icon) {
    // Remove old alerts
    const oldAlerts = document.querySelectorAll('.alert');
    oldAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.role = 'alert';
    alert.innerHTML = `
        <i class="bi ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert before the form
    const form = document.getElementById('loginForm');
    form.parentNode.insertBefore(alert, form);

    // Auto-hide after 5 seconds (except for success messages)
    if (type !== 'success') {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Toggle password visibility
function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

// Enhanced keyboard navigation
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    const loginButton = document.getElementById('loginButton');

    // Focus username field on page load
    usernameField.focus();

    // Enter key navigation
    usernameField.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            passwordField.focus();
        }
    });

    // Real-time validation feedback
    usernameField.addEventListener('input', function() {
        if (this.value.trim().length >= 3) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
        }
    });

    passwordField.addEventListener('input', function() {
        if (this.value.length >= 6) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
        }
    });

    // Caps Lock detection
    document.addEventListener('keydown', function(e) {
        if (e.getModifierState && e.getModifierState('CapsLock')) {
            showCapsLockWarning(true);
        }
    });

    document.addEventListener('keyup', function(e) {
        if (e.getModifierState && !e.getModifierState('CapsLock')) {
            showCapsLockWarning(false);
        }
    });
});

// Show/hide caps lock warning
function showCapsLockWarning(show) {
    let warning = document.getElementById('capsLockWarning');

    if (show && !warning) {
        warning = document.createElement('div');
        warning.id = 'capsLockWarning';
        warning.className = 'alert alert-warning mt-2 mb-0';
        warning.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>Caps Lock is on';

        const passwordField = document.getElementById('password');
        passwordField.parentNode.appendChild(warning);
    } else if (!show && warning) {
        warning.remove();
    }
}

// Check if user is already logged in
if (localStorage.getItem('token')) {
    // Verify token is still valid
    fetch('/web/admin/api/beacons', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
    }).then(response => {
        if (response.ok) {
            // Token is valid, redirect to dashboard
            window.location.href = '/web/admin/beacons';
        } else {
            // Token is invalid, remove it
            localStorage.removeItem('token');
        }
    }).catch(() => {
        // Network error, keep user on login page
    });
}