// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();

    // Get form data
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        // Send login request
        const response = await fetch('/web/admin/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Save token to localStorage
            localStorage.setItem('token', data.token);
            // Login successful, redirect to beacons management page
            window.location.href = '/web/admin/beacons';
        } else {
            // Login failed, show error message
            showError(data.error || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Network error, please try again later');
    }

    return false;
}

// Show error message
function showError(message) {
    // Remove old error alert
    const oldAlert = document.querySelector('.alert');
    if (oldAlert) {
        oldAlert.remove();
    }

    // Create new error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.role = 'alert';
    alert.textContent = message;

    // Insert before the form
    const form = document.getElementById('loginForm');
    form.parentNode.insertBefore(alert, form);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        alert.remove();
    }, 3000);
}