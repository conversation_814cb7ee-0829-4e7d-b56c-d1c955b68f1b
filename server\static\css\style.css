/* ===== CSS CUSTOM PROPERTIES (VARIABLES) ===== */
:root {
    /* Color Palette */
    --primary-color: #2c3e50;
    --primary-dark: #1a252f;
    --primary-light: #34495e;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;
    --bg-light: #ecf0f1;

    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    --text-white: #ffffff;

    /* Border & Shadow */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-color: #dee2e6;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;

    /* Typography */
    --font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-family-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== NAVIGATION STYLES ===== */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    box-shadow: var(--box-shadow);
    border: none;
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
    color: var(--text-white) !important;
    text-decoration: none;
    transition: var(--transition-base);
}

.navbar-brand:hover {
    color: var(--secondary-color) !important;
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: var(--text-white) !important;
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-base);
    margin: 0 var(--spacing-xs);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--secondary-color) !important;
}

/* ===== CARD STYLES ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
    background-color: var(--bg-primary);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

/* ===== FORM STYLES ===== */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
    background-color: var(--bg-primary);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* ===== BUTTON STYLES ===== */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--transition-base);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
    color: var(--text-white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-dark {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-white);
}

.btn-dark:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #0f1419 100%);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e8449 100%);
    color: var(--text-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d68910 100%);
    color: var(--text-white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
    color: var(--text-white);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: var(--text-white);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* ===== INPUT GROUP STYLES ===== */
.input-group-text {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border: 2px solid var(--border-color);
    border-right: none;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.input-group .form-control {
    border-left: none;
}

.input-group .form-control:focus + .input-group-text,
.input-group-text:has(+ .form-control:focus) {
    border-color: var(--secondary-color);
}

/* ===== ALERT STYLES ===== */
.alert {
    border-radius: var(--border-radius-sm);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-medium);
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(30, 132, 73, 0.1) 100%);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(214, 137, 16, 0.1) 100%);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(20, 143, 162, 0.1) 100%);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* ===== TABLE STYLES ===== */
.table {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-white);
    font-weight: var(--font-weight-semibold);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-color: var(--border-color);
    vertical-align: middle;
}

/* ===== BADGE STYLES ===== */
.badge {
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

/* ===== MODAL STYLES ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--border-color);
    padding: var(--spacing-lg);
}

.modal-title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

.rounded-modern {
    border-radius: var(--border-radius) !important;
}

.rounded-modern-sm {
    border-radius: var(--border-radius-sm) !important;
}

.rounded-modern-lg {
    border-radius: var(--border-radius-lg) !important;
}

/* ===== STATUS INDICATORS ===== */
.status-online {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-warning {
    color: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-offline {
    color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* ===== LOADING ANIMATIONS ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: var(--spacing-md);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .table-responsive {
        border-radius: var(--border-radius-sm);
    }

    .navbar-brand {
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 576px) {
    .container {
        padding: var(--spacing-sm);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .modal-dialog {
        margin: var(--spacing-sm);
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
        --border-color: #404040;
    }

    .card {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }

    .form-control {
        background-color: var(--bg-primary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }

    .table {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        background: white;
        color: black;
    }
}