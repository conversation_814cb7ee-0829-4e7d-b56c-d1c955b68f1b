/* ===== MODERN UI DESIGN SYSTEM ===== */
:root {
    /* Premium Color Palette - Dark Theme */
    --primary-900: #0f172a;
    --primary-800: #1e293b;
    --primary-700: #334155;
    --primary-600: #475569;
    --primary-500: #64748b;
    --primary-400: #94a3b8;
    --primary-300: #cbd5e1;
    --primary-200: #e2e8f0;
    --primary-100: #f1f5f9;
    --primary-50: #f8fafc;

    /* Accent Colors */
    --accent-purple: #8b5cf6;
    --accent-purple-light: #a78bfa;
    --accent-purple-dark: #7c3aed;
    --accent-blue: #3b82f6;
    --accent-blue-light: #60a5fa;
    --accent-blue-dark: #2563eb;
    --accent-emerald: #10b981;
    --accent-emerald-light: #34d399;
    --accent-emerald-dark: #059669;
    --accent-amber: #f59e0b;
    --accent-amber-light: #fbbf24;
    --accent-amber-dark: #d97706;
    --accent-red: #ef4444;
    --accent-red-light: #f87171;
    --accent-red-dark: #dc2626;

    /* Semantic Colors */
    --success: var(--accent-emerald);
    --success-light: var(--accent-emerald-light);
    --success-dark: var(--accent-emerald-dark);
    --warning: var(--accent-amber);
    --warning-light: var(--accent-amber-light);
    --warning-dark: var(--accent-amber-dark);
    --danger: var(--accent-red);
    --danger-light: var(--accent-red-light);
    --danger-dark: var(--accent-red-dark);
    --info: var(--accent-blue);
    --info-light: var(--accent-blue-light);
    --info-dark: var(--accent-blue-dark);

    /* Background System */
    --bg-primary: #ffffff;
    --bg-secondary: var(--primary-50);
    --bg-tertiary: var(--primary-100);
    --bg-dark: var(--primary-900);
    --bg-card: #ffffff;
    --bg-overlay: rgba(15, 23, 42, 0.8);
    --bg-glass: rgba(255, 255, 255, 0.1);

    /* Text System */
    --text-primary: var(--primary-900);
    --text-secondary: var(--primary-600);
    --text-tertiary: var(--primary-500);
    --text-muted: var(--primary-400);
    --text-white: #ffffff;
    --text-inverse: var(--primary-50);

    /* Border & Shadow System */
    --border-radius-xs: 4px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-full: 9999px;

    --border-width: 1px;
    --border-color: var(--primary-200);
    --border-color-hover: var(--primary-300);
    --border-color-focus: var(--accent-blue);

    /* Advanced Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3);
    --shadow-colored: 0 10px 15px -3px rgba(139, 92, 246, 0.1), 0 4px 6px -4px rgba(139, 92, 246, 0.1);

    /* Spacing System */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* Typography System */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Animation System */
    --duration-75: 75ms;
    --duration-100: 100ms;
    --duration-150: 150ms;
    --duration-200: 200ms;
    --duration-300: 300ms;
    --duration-500: 500ms;
    --duration-700: 700ms;
    --duration-1000: 1000ms;

    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Z-Index System */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* ===== MODERN SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-100);
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-300);
    border-radius: var(--border-radius-full);
    transition: background var(--duration-200) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-400);
}

/* ===== SELECTION STYLES ===== */
::selection {
    background: rgba(139, 92, 246, 0.2);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(139, 92, 246, 0.2);
    color: var(--text-primary);
}

/* ===== FOCUS STYLES ===== */
:focus {
    outline: 2px solid var(--accent-purple);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 2px solid var(--accent-purple);
    outline-offset: 2px;
}

/* ===== MODERN NAVIGATION ===== */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    padding: var(--space-4) 0;
    transition: all var(--duration-300) var(--ease-out);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-lg);
}

.navbar-brand {
    font-family: var(--font-family-sans);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
    color: var(--text-primary) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: all var(--duration-200) var(--ease-out);
}

.navbar-brand:hover {
    color: var(--accent-purple) !important;
    transform: translateY(-1px);
}

.navbar-brand i {
    font-size: var(--font-size-2xl);
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--border-radius-lg);
    transition: all var(--duration-200) var(--ease-out);
    margin: 0 var(--space-1);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
    opacity: 0;
    transition: opacity var(--duration-200) var(--ease-out);
    z-index: -1;
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    opacity: 0.1;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--accent-purple) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link i {
    margin-right: var(--space-1);
    font-size: var(--font-size-sm);
}

/* Dropdown Styles */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
    min-width: 200px;
}

.dropdown-item {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--border-radius-md);
    transition: all var(--duration-150) var(--ease-out);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dropdown-item:hover {
    background: var(--primary-50);
    color: var(--accent-purple);
    transform: translateX(2px);
}

.dropdown-divider {
    border-color: var(--border-color);
    margin: var(--space-2) 0;
}

/* ===== MODERN CARD SYSTEM ===== */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-300) var(--ease-out);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-purple), var(--accent-blue), var(--accent-emerald));
    opacity: 0;
    transition: opacity var(--duration-300) var(--ease-out);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-color-hover);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: var(--space-6);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    position: relative;
}

.card-header h5 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-header h5 i {
    color: var(--accent-purple);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    background: var(--primary-50);
    border-top: 1px solid var(--border-color);
    padding: var(--space-4) var(--space-6);
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--primary-50) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: var(--space-6);
    transition: all var(--duration-300) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(139, 92, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--duration-300) var(--ease-out);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-purple);
}

.stats-card:hover::before {
    opacity: 1;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-3);
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    border-radius: inherit;
}

.stats-icon.success::before {
    background: var(--success);
}

.stats-icon.warning::before {
    background: var(--warning);
}

.stats-icon.danger::before {
    background: var(--danger);
}

.stats-icon.info::before {
    background: var(--info);
}

.stats-icon i {
    color: inherit;
    z-index: 1;
    position: relative;
}

.stats-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-1);
}

.stats-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== FORM STYLES ===== */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
    background-color: var(--bg-primary);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* ===== MODERN BUTTON SYSTEM ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-family-sans);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--duration-200) var(--ease-out);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-500) var(--ease-out);
}

.btn:hover::before {
    left: 100%;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-purple) 0%, var(--accent-blue) 100%);
    color: var(--text-white);
    border-color: var(--accent-purple);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-purple-dark) 0%, var(--accent-blue-dark) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Secondary Button */
.btn-secondary {
    background: var(--bg-card);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--primary-50);
    color: var(--text-primary);
    border-color: var(--border-color-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-dark) 100%);
    color: var(--text-white);
    border-color: var(--success);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-dark) 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Warning Button */
.btn-warning {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning-dark) 100%);
    color: var(--text-white);
    border-color: var(--warning);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-dark) 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, var(--danger-dark) 100%);
    color: var(--text-white);
    border-color: var(--danger);
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-dark) 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Info Button */
.btn-info {
    background: linear-gradient(135deg, var(--info) 0%, var(--info-dark) 100%);
    color: var(--text-white);
    border-color: var(--info);
}

.btn-info:hover {
    background: linear-gradient(135deg, var(--info-dark) 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Outline Buttons */
.btn-outline-primary {
    background: transparent;
    color: var(--accent-purple);
    border-color: var(--accent-purple);
}

.btn-outline-primary:hover {
    background: var(--accent-purple);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background: var(--primary-100);
    color: var(--text-primary);
    border-color: var(--border-color-hover);
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-xl);
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
    border-right-width: 1px;
}

.btn-group .btn:hover {
    z-index: 1;
    border-right-width: 1px;
}

/* Loading Button */
.btn.loading {
    pointer-events: none;
}

.btn .spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

/* ===== INPUT GROUP STYLES ===== */
.input-group-text {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border: 2px solid var(--border-color);
    border-right: none;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.input-group .form-control {
    border-left: none;
}

.input-group .form-control:focus + .input-group-text,
.input-group-text:has(+ .form-control:focus) {
    border-color: var(--secondary-color);
}

/* ===== ALERT STYLES ===== */
.alert {
    border-radius: var(--border-radius-sm);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-medium);
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(30, 132, 73, 0.1) 100%);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(214, 137, 16, 0.1) 100%);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(20, 143, 162, 0.1) 100%);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* ===== TABLE STYLES ===== */
.table {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-white);
    font-weight: var(--font-weight-semibold);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-color: var(--border-color);
    vertical-align: middle;
}

/* ===== BADGE STYLES ===== */
.badge {
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

/* ===== MODAL STYLES ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--border-color);
    padding: var(--spacing-lg);
}

.modal-title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

.rounded-modern {
    border-radius: var(--border-radius) !important;
}

.rounded-modern-sm {
    border-radius: var(--border-radius-sm) !important;
}

.rounded-modern-lg {
    border-radius: var(--border-radius-lg) !important;
}

/* ===== STATUS INDICATORS ===== */
.status-online {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-warning {
    color: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-offline {
    color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* ===== LOADING ANIMATIONS ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: var(--spacing-md);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .table-responsive {
        border-radius: var(--border-radius-sm);
    }

    .navbar-brand {
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 576px) {
    .container {
        padding: var(--spacing-sm);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .modal-dialog {
        margin: var(--spacing-sm);
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
        --border-color: #404040;
    }

    .card {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }

    .form-control {
        background-color: var(--bg-primary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }

    .table {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== DASHBOARD SPECIFIC STYLES ===== */
.dashboard-body {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    min-height: 100vh;
}

/* Enhanced Table Styles */
.table {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
    color: var(--text-white);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: none;
    padding: var(--space-4) var(--space-3);
    position: relative;
}

.table thead th.sortable:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.table tbody tr {
    transition: all var(--duration-200) var(--ease-out);
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%);
    transform: scale(1.005);
    box-shadow: var(--shadow-sm);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody td {
    padding: var(--space-4) var(--space-3);
    vertical-align: middle;
    border: none;
    font-size: var(--font-size-sm);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-dark);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-indicator.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-dark);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-indicator.offline {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-dark);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Beacon Info Styles */
.beacon-hostname {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.beacon-ip {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    background: var(--primary-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--border-radius-sm);
    color: var(--primary-700);
}

.beacon-user {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-secondary);
}

.beacon-process {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
}

.beacon-arch {
    background: var(--primary-600);
    color: var(--text-white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-1);
}

.action-buttons .btn {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
}

/* Loading and Empty States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.empty-state {
    padding: var(--space-16) var(--space-8);
    text-align: center;
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--primary-300);
    margin-bottom: var(--space-4);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.beacon-row {
    animation: fadeInUp var(--duration-300) var(--ease-out) forwards;
}

.stats-card {
    animation: fadeInUp var(--duration-300) var(--ease-out) forwards;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-body {
        padding: var(--space-2);
    }

    .stats-card {
        margin-bottom: var(--space-3);
    }

    .table-responsive {
        border-radius: var(--border-radius-lg);
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-buttons .btn {
        width: 100%;
        margin-bottom: var(--space-1);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .btn,
    .modal,
    .action-buttons {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .table thead th {
        background: #f8f9fa !important;
        color: #000 !important;
    }
}