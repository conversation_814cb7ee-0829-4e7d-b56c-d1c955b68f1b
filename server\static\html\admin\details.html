<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSentinel - Beacon Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">GateSentinel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/beacons">Beacon List</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>Beacon Details</h2>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col">
                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="goBack()">
                        <i class="bi bi-arrow-left"></i> Back
                    </button>
                    <button class="btn btn-danger" onclick="deleteBeacon()">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Basic Information</span>
                            <span id="beaconStatus" class="badge bg-success">Idle</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody id="basicInfo">
                                <!-- Basic information will be populated dynamically via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">Actions</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="taskType" class="form-label">Task Type</label>
                            <select class="form-select" id="taskType">
                                <option value="0x00">Keep Sleep</option>
                                <option value="0x1A">Set Sleep Time</option>
                                <option value="0x1B">Get Process List</option>
                                <option value="0x1C">Execute Shellcode</option>
                            </select>
                        </div>

                        <div id="sleepTimeDiv" style="display: none;" class="mb-3">
                            <label for="sleepTime" class="form-label">Sleep Time (seconds)</label>
                            <input type="number" class="form-control" id="sleepTime" min="1">
                        </div>

                        <div id="shellcodeDiv" style="display: none;" class="mb-3">
                            <label for="shellcodeFile" class="form-label">Shellcode File</label>
                            <input type="file" class="form-control" id="shellcodeFile">
                            <div class="form-text">Shellcode will be encrypted using the target Beacon's UUID</div>
                        </div>

                        <button class="btn btn-primary" id="sendTask">
                            <i class="bi bi-send"></i> Send Task
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">Task Results</div>
                    <div class="card-body">
                        <pre id="jobResult" class="mb-0">No task results available</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin/details.js"></script>
</body>
</html> 