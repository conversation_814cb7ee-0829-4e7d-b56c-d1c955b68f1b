package main

import (
	"io/fs"
	"net/http"
	"gatesentinel/handler"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

func setupRouter() *gin.Engine {
	r := gin.Default()

	// Set session middleware
	store := cookie.NewStore([]byte("secret"))
	r.Use(sessions.Sessions("gatesentinel", store))

	// 从嵌入的FS中获取static子目录
	staticFS, err := fs.Sub(staticFiles, "static")
	if err != nil {
		panic(err)
	}

	// 静态文件服务
	r.StaticFS("/static", http.FS(staticFS))

	// favicon.ico
	r.GET("/favicon.ico", func(c *gin.Context) {
		c.FileFromFS("favicon.ico", http.FS(staticFS))
	})

	// 默认路由到登录页
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/login")
	})

	// 登录页面
	r.GET("/login", func(c *gin.Context) {
		c.FileFromFS("html/login.html", http.FS(staticFS))
	})

	// Beacon相关路由
	r.POST("/register", handler.RegisterHandler)
	r.Any("/api.jsp", handler.BeaconHandler)

	// 管理后台API路由
	api := r.Group("/web/admin/api")
	{
		api.POST("/login", handler.AdminLoginHandler)

		// 需要登录验证的API
		authorized := api.Group("/", handler.AuthRequired())
		{
			authorized.GET("/beacons", handler.ListBeaconsHandler)
			authorized.GET("/beacons/:uuid", handler.GetBeaconHandler)
			authorized.POST("/beacons/:uuid/job", handler.UpdateBeaconJobHandler)
			authorized.POST("/beacons/:uuid/delete", handler.DeleteBeaconHandler)
			authorized.GET("/config", handler.GetConfigHandler)
			authorized.POST("/config", handler.UpdateConfigHandler)
		}
	}

	// 管理后台页面路由
	admin := r.Group("/web/admin")
	{
		// 处理根路径，需要登录验证并重定向到beacons页面
		admin.GET("", handler.AuthRequired(), func(c *gin.Context) {
			c.Redirect(http.StatusFound, "/web/admin/beacons")
		})

		// Beacons列表页面
		admin.GET("/beacons", handler.AuthRequired(), func(c *gin.Context) {
			c.FileFromFS("html/admin/beacons.html", http.FS(staticFS))
		})

		// 其他需要登录验证的页面
		authorized := admin.Group("/", handler.AuthRequired())
		{
			authorized.GET("details/:uuid", func(c *gin.Context) {
				c.FileFromFS("html/admin/details.html", http.FS(staticFS))
			})
			authorized.GET("settings", func(c *gin.Context) {
				c.FileFromFS("html/admin/settings.html", http.FS(staticFS))
			})
		}
	}

	return r
}
